<template>
  <div class="h-full w-full" ref="container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as THREE from "three";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
// 导入轨道控制器
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import { GUI } from "three/examples/jsm/libs/lil-gui.module.min.js";

// 获取容器引用
const container = ref<HTMLDivElement>();

// Three.js 相关变量
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let poolModel: THREE.Group | null = null;
let animationId: number;

// 初始化 Three.js
function initThreeJS() {
  if (!container.value) return;

  // 获取容器尺寸
  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  // 创建场景
  scene = new THREE.Scene();

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    45, // 视角
    width / height, // 宽高比
    0.1, // 近平面
    1000 // 远平面
  );

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  renderer.setPixelRatio(window.devicePixelRatio);

  // 将渲染器添加到容器中
  container.value.appendChild(renderer.domElement);

  // 添加光源
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(10, 10, 5);
  scene.add(directionalLight);

  // 加载FBX模型
  loadPoolModel();

  // 设置相机位置
  camera.position.set(0, 4, 0);
  camera.lookAt(0, 0, 0);

  // 创建轨道控制器
  new OrbitControls(camera, renderer.domElement);
  // 添加坐标轴辅助器
  const axesHelper = new THREE.AxesHelper(5);
  scene.add(axesHelper);

  //创建GUI
  const enventObj = {
    toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.body.requestFullscreen();
      } else {
        document.exitFullscreen();
      }
    },
  };
  const gui = new GUI();

  //添加按钮
  gui.add(enventObj, "toggleFullscreen").name("全屏切换");

  // 开始动画
  animate();
}

// 加载GLB模型
function loadPoolModel() {
  const loader = new GLTFLoader();

  // 使用指定路径
  const modelPath = "/coffee.glb";
  console.log(`Loading model from: ${modelPath}`);

  loader.load(
    modelPath,
    (gltf) => {
      poolModel = gltf.scene;

      // 调整模型大小和位置 - 增大缩放比例
      gltf.scene.scale.setScalar(0.07);
      gltf.scene.position.set(0, 0, 0);

      // 将模型旋转90度（绕Y轴旋转）
      gltf.scene.rotation.y = Math.PI / 4;

      // 添加到场景
      scene.add(gltf.scene);

      console.log("🚀 ~ loadPoolModel ~ gltf.scene:", gltf.scene);
      console.log(`Model loaded successfully from: ${modelPath}`);
    },
    (progress) => {
      console.log(
        `Loading progress:`,
        (progress.loaded / progress.total) * 100 + "%"
      );
    },
    (error) => {
      console.error(`Error loading model:`, error);
    }
  );
}

// 渲染函数
function animate() {
  animationId = requestAnimationFrame(animate);
  renderer.render(scene, camera);
}

// 处理窗口大小变化
function handleResize() {
  if (!container.value || !camera || !renderer) return;

  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);
}

// 清理资源
function cleanup() {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }

  // 清理模型
  if (poolModel) {
    scene.remove(poolModel);
    poolModel = null;
  }

  if (renderer) {
    renderer.dispose();
  }

  window.removeEventListener("resize", handleResize);
}

// 组件挂载时初始化
onMounted(() => {
  initThreeJS();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>
