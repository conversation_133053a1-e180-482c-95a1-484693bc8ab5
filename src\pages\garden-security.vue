<template>
  <div class="system-container">
    <div class="iframe-container">
      <iframe
        src="http://*************:8123/index15.html"
        class="warehouse-iframe"
        frameborder="0"
        allowfullscreen
        @load="onIframeLoad"
      ></iframe>
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>正在加载园区安防系统...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 加载状态
const loading = ref(true);

// iframe加载完成事件
const onIframeLoad = () => {
  loading.value = false;
};
</script>

<style scoped>
.system-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.iframe-container {
  flex: 1;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(102, 204, 255, 0.05);
  border: 2px solid rgba(102, 204, 255, 0.2);
  box-shadow: 0 8px 32px rgba(102, 204, 255, 0.1);
}

.warehouse-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #fff;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(102, 204, 255, 0.8);
  z-index: 10;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(102, 204, 255, 0.3);
  border-top: 3px solid #66ccff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-overlay p {
  font-size: 16px;
  color: rgba(102, 204, 255, 0.7);
  letter-spacing: 1px;
  margin: 0;
}
</style>
