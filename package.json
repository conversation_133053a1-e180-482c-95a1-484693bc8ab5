{"name": "big-data-screen", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@iconify-json/mdi": "^1.2.3", "@iconify/vue": "^5.0.0", "@loaders.gl/core": "^4.3.4", "@loaders.gl/draco": "^4.3.4", "@loaders.gl/gltf": "^4.3.4", "@types/three": "^0.178.1", "@unocss/reset": "^66.3.3", "autofit.js": "^3.2.8", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "three": "^0.179.0", "three-fbx-loader": "^1.0.3", "three-stdlib": "^2.36.0", "vue": "^3.5.17"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/dat.gui": "^0.7.13", "@types/node": "^24.0.15", "@unocss/eslint-config": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "sass": "^1.89.2", "typescript": "~5.8.3", "typescript-eslint": "^8.37.0", "unocss": "^66.3.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}